package com.carsaver.partner.web.api.user;

import com.carsaver.elasticsearch.model.LeadDoc;
import com.carsaver.elasticsearch.service.LeadDocService;
import com.carsaver.magellan.api.deal.DealSheetService;
import com.carsaver.magellan.client.CertificateClient;
import com.carsaver.magellan.client.UserClient;
import com.carsaver.magellan.model.CertificateView;
import com.carsaver.magellan.model.DealJacket;
import com.carsaver.magellan.model.UserView;
import com.carsaver.partner.elasticsearch.criteria.LeadSearchCriteria;
import com.carsaver.partner.model.user.UserLead;
import com.carsaver.partner.service.adf.AdfService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Optional;

@Slf4j
@Service
@AllArgsConstructor
public class UserLeadService {

    public static final String DOC_TYPE_PROSPECT = "PROSPECT";

    private UserClient userClient;
    private LeadDocService leadDocService;
    private final AdfService adfService;
    private CertificateClient certificateClient;
    private DealSheetService dealSheetService;


    public UserLead getUserLeads(List<String> dealers, String userId, Pageable pageable){

        UserView user = userClient.findById(userId);

        //get elastic search lead docs
        Collection<LeadDoc> leadDocs = searchLeadDocs(dealers, userId, pageable);
        findProspectLeads(pageable, user, leadDocs);
        if (leadDocs.isEmpty()) {
            log.info("No leads found for User {} & Dealer {}", userId , String.join(",", dealers));
            return null;
        }

        //loop through lead docs and build FE response
        List<UserLead.Lead> userLeads = new ArrayList<>();
        leadDocs.forEach(leadDoc -> {
                try {
                    if (DOC_TYPE_PROSPECT.equalsIgnoreCase(leadDoc.getDocType())) {
                        String adf = adfService.fetchProspectLeadTransactionAdf(leadDoc.getId());
                        userLeads.add(UserLead.Lead.from(leadDoc, adf));
                    } else if (leadDoc.getCertificate() == null) {
                        String adf = adfService.fetchDealerLeadTransactionAdf(leadDoc.getId());
                        userLeads.add(UserLead.Lead.from(leadDoc, adf));
                    }
                    else {
                        String adf = adfService.fetchDealerLeadTransactionAdf(leadDoc.getId());
                        CertificateView certificateView = certificateClient.findById(leadDoc.getCertificate().getId());
                        // leads that needed a certificate to work but were not actual deals. Aka all trade leads
                        if (certificateView.getInventoryId() == null) {
                            userLeads.add(UserLead.Lead.from(leadDoc, adf));
                        } else {
                            Optional<DealJacket> dealJacketOpt = dealSheetService.toDealJacket(certificateView);
                            var dealJacket = dealJacketOpt.orElse(null);
                            userLeads.add(UserLead.Lead.from(leadDoc, certificateView, dealJacket, adf));
                        }

                    }
                } catch (Exception e) {
                    log.error("Exception thrown for leads for user {} and lead {}. Error: ", userId , leadDoc, e);
                }
            });

        return UserLead.builder().leads(userLeads).build();
    }

    private Collection<LeadDoc> searchLeadDocs(List<String> dealers, String userId, Pageable pageable){
        LeadSearchCriteria criteria = new LeadSearchCriteria();
        criteria.setUserId(userId);
        criteria.setDealerIds(dealers);

        return leadDocService.search(criteria, pageable).getContent();
    }

    public void findProspectLeads(Pageable pageable, UserView user, Collection<LeadDoc> leadDocs) {
        if (user != null && user.getSource() != null) {
            String prospectId = user.getUpgradeProspectId();
            String whispProspectId = user.getSource().getWhispProspectId();

            if (prospectId != null || whispProspectId != null) {
                LeadSearchCriteria criteria = new LeadSearchCriteria();
                criteria.setUserId(whispProspectId != null ? whispProspectId : prospectId);
                criteria.setDocType(DOC_TYPE_PROSPECT);
                criteria.setDealerIds(null);
                Collection<LeadDoc> prospectLeadDocs = leadDocService.search(criteria, pageable).getContent();
                leadDocs.addAll(prospectLeadDocs);
            }
        }
    }
}
