package com.carsaver.partner.service.split_io;

import com.carsaver.magellan.auth.SessionUtils;
import com.carsaver.magellan.client.ProgramClient;
import com.carsaver.magellan.model.UserView;
import com.carsaver.magellan.model.campaign.CampaignView;
import com.carsaver.magellan.model.foundation.ProductView;
import com.carsaver.magellan.model.foundation.ProgramView;
import com.carsaver.split.io.client.SplitClientFacade;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@Slf4j
@Service
public class SplitFeatureFlags {
    private static final String NISSAN = "Nissan";
    private static final String ATLAS = "Atlas";
    private static final String DEAL_EDITING_CASH_FEATURE = "dealEditingCash";
    private static final String TRADE_IN_ADJUSTMENT_MULTIPLE_DEALERS_FEATURE = "tradeInAdjustmentMultipleDealersFeature";
    private static final String OFFER_ADJUSTMENT_FEATURE = "offerAdjustmentFeature";
    private static final String OFFER_ADJUSTMENT_FEATUREV2 = "offerAdjustmentFeatureV2";
    public static final String PAAS_INTEGRATION = "paasIntegration";
    public static final String SELL_AT_HOME_FEATURE = "sellAtHomeFeature";
    public static final String DISPLAY_OWNED_PROSPECTS_FOR_DEALER_USERS = "atlasDisplayOwnedProspectForDealerUsers";
    public static final String CRM_LEADS_FOR_PROSPECTS = "atlasCRMLeadsForProspectsEnabled";
    public static final String VEHICLE_URL_DEALERIZE_VERSION = "atlasVehicleUrlDealerizeVersion";
    public static final String ENABLE_LEASE_PAYOFF = "enableLeasePayoff";
    private static final String ENABLE_ELIGIBLE_PROSPECT_LEAD_CREATION = "enableEligibleProspectLeadCreation";
    public static final String NEW_CONFIG_MANAGER = "NewConfigManager";
    private static final String ENABLE_STAGE_TILES_FOR_DEALERS = "enableStageTilesForDealers";
    private static final String ENABLE_STAGE_TILES_FOR_ADMINS = "EnableStageTilesForAdmins";
    public static final String ROUTE_ONE_PROTECTION_PRODUCT_STAGING_HOST_ENABLED = "routeOneProtectionProductStagingHostEnabled";
    private static final String LMS_PREFERENCE_FEATURE = "LMSPreferenceFeature";
    public static final String UPGRADE = "Upgrade";
    public static final String DIGITAL_RETAIL_ENABLED = "enableDigitalRetail";
    public static final String ATLAS_CUSTOMER_SORT_FIELDS_ENABLED = "AtlasCustomerSortFieldsEnabled";
    public static final String ATLAS_ENHANCED_NO_GROUP_FILTERS = "AtlasEnhancedNoGroupFilters";
    public static final String ATLAS_SECOND_PHASE_FILTERS = "AtlasSecondPhaseFilters";
    public static final String ATLAS_RANGE_SLIDER_FILTERS = "AtlasRangeSliderFilters";
    public static final String ATLAS_CUSTOMER_BMW_NEW_COLUMNS = "AtlasCustomerPageBMWNewColumns";
    public static final String ENABLE_ATLAS_TRADE_PURCHASE_FIELDS = "enableAtlasTradePurchaseFields";
    public static final String ENABLE_NESNA_F_AND_I = "EnableNesnaFandI";
    private static final String ATLAS_LOGIN_SANITIZATION_ENABLE = "AtlasLoginSanitizationEnable";
    public static final String DOMO_INTEGRATION = "DomoEmbedAtlas";
    private static final String ATLAS_DEALER_TRACK_PHASE2_ENABLED = "dealerTrackPhase2Enabled";
    private static final String SEND_TO_CRM_ENHANCEMENT = "SendToCrmEnhancement";
    private static final String ATLAS_NEW_CUSTOMER_PAGE = "AtlasNewCustomerPage";
    private static final String ADAPTIVE_RETAIL_PLAYGROUND = "AdaptiveRetailPlayground";
    private static final String ATLAS_ACTION_BAR_ENABLED = "AtlasActionBarEnabled";
    private static final String ATLAS_REQUEST_SCREEN_SHARE = "AtlasRequestScreenShare";
    private static final String IMPORT_CAR_LOGS_BY_DEALER_V2 = "ImportCarLogsByDealerV2";
    private static final String ATLAS_CUSTOMER_LEADS_DISPLAY_ADF_ENABLED = "atlasCustomerLeadsDisplayAdfEnabled";
    private static final String ENABLE_IN_APP_GARAGE_ALERTS_FEATURE = "EnableINAppGarageAlerts";
    private static final String ENABLE_GARAGE_ALERTS_FEATURE = "EnableGarageAlerts";
    private static final String ENABLE_SMS_GARAGE_ALERTS_FEATURE = "EnableSMSGarageAlerts";
    private static final String ENABLE_EMAIL_GARAGE_ALERTS_FEATURE = "EnableEmailGarageAlerts";
    private static final String ATLAS_STICKY_FILTERS_ENABLED = "AtlasStickyFiltersEnabled";
    private static final String ENABLE_ELASTIC_NESTED_FIELDS = "enableElasticNestedFields";
    private static final String ATLAS_FAST_PASS_ENABLED = "AtlasFastPassEnabled";
    private static final String ATLAS_NEW_CUSTOMER_PAGE_ONLINE_NOW = "AtlasNewCustomerPageOnlineNow";
    private static final String ATLAS_NEW_CUSTOMER_PAGE_IN_SHOWROOM = "AtlasNewCustomerPageInShowroom";
    private static final String CUSTOMER_TAGS_ENHANCEMENT_TOGGLE = "CustomerTagsEnhancementsToggle";
    private static final String DRAWER_SETTINGS_ENABLED = "DrawerSettingsEnabled";
    private static final String QR_CODE_SETTINGS_ENABLED = "QrCodeSettingsEnabled";
    private static final String CUSTOMER_INFO_CARD_PROGRAM_USER_SUPPORT_TOGGLE = "customerInfoCardProgramUserSupportToggle";
    private static final String PROGRAM_USER_SUPPORT = "ProgramUserSupport";
    private static final String ATLAS_WALMART_INVENTORY_ENABLED = "AtlasWalmartInventoryEnabled";
    private static final String ATLAS_ENABLED_LEAD_FILTER = "AtlasEnableLeadFilter";
    private static final String ATLAS_IN_APP_NOTIFICATION_ENABLED = "AtlasInAppNotificationEnabled";
    private static final String ENABLE_LANGUAGE_SELECTION = "EnableLanguageSelection";
    private static final String FINANCE_NEGATIVE_BUG = "FinanceNegativeBug";
    private static final String WHISP_LEAD_ENABLED = "whispLeadEnabled";
    private static final String ENABLE_CARSAVER_F_AND_I = "EnableCarsaverFandI";

    private static final String CUSTOMER_DETAILS_PRE_QUALIFICATIONS = "CustomerDetailsPreQualifications";
    private static final String ENABLE_AR_NOTES = "EnableARNotes";
    private static final String ATLAS_NEW_CUSTOMER_DETAILS_PAGE_CURRENT_VEHICLES = "AtlasNewCustomerDetailsPageCurrentVehicles";
    private static final String ENABLED_AR_CONTRACTS = "EnableARContracts";
    private static final String ATLAS_NEW_CUSTOMER_PAGE_IN_SHOWROOM_V2 = "AtlasNewCustomerPageCurrentInshowRoomV2";
    private static final String ENABLE_AR_FINANCE_APP = "EnableARFinanceApp";
    private static final String ENABLE_AR_ACTIVITY = "EnableARActivity";
    private static final String CTA_CONFIG_MENU = "CTAConfigMenu";
    private static final String PROGRAM_RETURN_POLICY_FEATURE = "ProgramReturnPolicyFeature";
    private static final String ENABLE_AR_SEARCHES = "EnableARSearches";
    private static final String ENABLE_CLIENT_THEMING = "enableClientTheming";
    private static final String ENABLE_OVERLAY_SETTINGS = "enableOverlaySettings";
    private static final String ENABLE_MDP_CONFIGS = "EnableMDPConfigs";
    private static final String ENABLE_DEALER_SALES_PERSON = "EnableCheckInSalesPerson";
    private static final String ENABLE_DEALER_PROGRAM_URLS = "EnabledDealerProgramUrls";
    private static final String ENABLE_DEALER_RATE_SHEET = "EnableDealerRateSheet";
    private static final String ATLAS_DEALER_TRACK_TOGGLE = "AtlasDealerTrackToggle";
    private static final String ENABLE_SMS_ONLINE_NOW = "EnableSMSOnlineNow";
    public static final String ATLAS_LEAD_TYPE_CONFIG = "AtlasLeadTypeConfig";
    public static final String BOOST_PLUS_FEATURES_ENABLED = "BoostPlusFeaturesEnabled";
    public static final String ENABLE_GRAPHICS_AND_IMAGES = "EnableGraphicsAndImages";
    public static final String ATLAS_BANNER_CONFIG = "AtlasBannerConfig";
    public static final String ATLAS_SERVICE_DRIVE_TOGGLE = "AtlasServiceDriveToggle";


    private final SplitClientFacade splitClientFacade;
    private final Environment environment;

    public static final Integer UPGRADE_PRODUCT_ID = 103;
    public static final Integer ECOMMERCE_PRODUCT_ID = 102;

    private final ProgramClient programClient;

    public SplitFeatureFlags(SplitClientFacade splitClientFacade, Environment environment,
            ProgramClient programClient) {
        this.splitClientFacade = splitClientFacade;
        this.environment = environment;
        this.programClient = programClient;
    }

    public boolean isDomoIntegrationEnabled() {
        Optional<UserView> loggedInUser = SessionUtils.getLoggedInUser();
        Map<String, String> attributes = new HashMap<>(Map.of("application", ATLAS));
        loggedInUser.ifPresent(user -> attributes.put("email", user.getEmail()));

        return isFeatureEnabled(ATLAS, DOMO_INTEGRATION, attributes);
    }

    public boolean isTradeInAdjustmentMultipleDealersFeature() {
        Map attributes = Map.of("program", "Nissan", "environment", getProfile());
        boolean enabled = isFeatureEnabled(NISSAN, TRADE_IN_ADJUSTMENT_MULTIPLE_DEALERS_FEATURE, attributes);
        return enabled;
    }

    public boolean isFinanceNegativeTest() {
        Map attributes = Map.of("program", "Nissan");
        boolean enabled = isFeatureEnabled(NISSAN, FINANCE_NEGATIVE_BUG, attributes);
        return enabled;
    }

    public boolean isOfferAdjustmentFeatureEnabled() {
        Map attributes = Map.of("program", "Nissan", "environment", getProfile());
        boolean enabled = isFeatureEnabled(NISSAN, OFFER_ADJUSTMENT_FEATURE, attributes);
        return enabled;
    }

    public boolean isEnableEligibleProspectLeadCreation(String dealerId) {
        Map<String, String> attr = new HashMap<>();
        attr.put("dealerId", dealerId);
        return isFeatureEnabled(UPGRADE, ENABLE_ELIGIBLE_PROSPECT_LEAD_CREATION, attr);
    }

    public boolean isAtlasInShowRoomV2Enabled() {
        boolean enabled = isFeatureEnabled(ATLAS, ATLAS_NEW_CUSTOMER_PAGE_IN_SHOWROOM);
        return enabled;
    }


    public boolean isPaasIntegrationEnabled(String userId, String dealerId, String campaignId) {
        String product = NISSAN;
        Map attributes = Map.of("program", product,
                "application", ATLAS,
                "user", userId,
                "dealer", Optional.ofNullable(dealerId).orElse(""),
                "campaignId", Optional.ofNullable(campaignId).orElse(""));
        return isFeatureEnabled(product, PAAS_INTEGRATION, attributes);
    }

    public boolean isSellAtHomeFeatureEnabled() {
        Map attributes = Map.of("program", "Nissan", "environment", getProfile());
        boolean enabled = isFeatureEnabled(NISSAN, SELL_AT_HOME_FEATURE, attributes);
        return enabled;
    }

    public boolean isEnableLanguageSelection() {
        Map attributes = Map.of("program", "Nissan","environment", getProfile());
        boolean enabled = isFeatureEnabled(NISSAN, ENABLE_LANGUAGE_SELECTION, attributes);
        return enabled;
    }

    public boolean isNesnaFAndIFeatureEnabled() {
        Map attributes = Map.of("program", "Nissan", "environment", getProfile());
        boolean enabled = isFeatureEnabled(NISSAN, ENABLE_NESNA_F_AND_I, attributes);
        return enabled;
    }

    public boolean isCustomerInfoCardProgramUserSupportToggle() {
        boolean enabled = isFeatureEnabled(ATLAS, CUSTOMER_INFO_CARD_PROGRAM_USER_SUPPORT_TOGGLE);
        return enabled;
    }

    public boolean isProgramUserSupportEnabled() {
        boolean enabled = isFeatureEnabled(ATLAS, PROGRAM_USER_SUPPORT);
        return enabled;
    }

    public boolean isLMSPreferenceEnabled() {
        Map attributes = Map.of("program", "Nissan", "environment", getProfile());
        boolean enabled = isFeatureEnabled(NISSAN, LMS_PREFERENCE_FEATURE, attributes);

        return enabled;
    }

    public boolean isLeasePayoffEnabled() {
        return isFeatureEnabled(UPGRADE, ENABLE_LEASE_PAYOFF);
    }

    public boolean isDealerizedProspectFeatureEnabled(String userId) {
        Map attributes = Map.of("application", ATLAS, "user", userId);
        return isFeatureEnabled(ATLAS, DISPLAY_OWNED_PROSPECTS_FOR_DEALER_USERS, attributes);
    }

    public boolean atlasCustomerLeadsDisplayAdfEnabled() {
        boolean enabled = isFeatureEnabled(ATLAS, ATLAS_CUSTOMER_LEADS_DISPLAY_ADF_ENABLED);
        return enabled;
    }

    public boolean isDigitalRetailEnabledForDealer(String dealerId, String vin) {
        Map<String, String> attributes = new HashMap<>(Map.of("application", ATLAS, "dealerId", dealerId));
        if (vin != null) {
            attributes.put("vin", vin);
        }
        return isFeatureEnabled(ATLAS, DIGITAL_RETAIL_ENABLED, attributes);
    }

    // only for unauthenticated users
    public Map<String, Boolean> getUnauthenticatedUserFeatureFlags() {
        Map<String, Boolean> featureFlags = new HashMap<>();

        featureFlags.put("ATLAS_LOGIN_SANITIZATION_ENABLE", isFeatureEnabled(ATLAS, ATLAS_LOGIN_SANITIZATION_ENABLE));

        return featureFlags;
    }

    public Boolean isInAppAlertsFeatureEnabled(String dealerId, String programId) {
        Map<String, String> attributes = new HashMap<>(
                Map.of("application", ATLAS, "dealerId", dealerId, "programId", programId));
        boolean enabled = isFeatureEnabled(ATLAS, ENABLE_IN_APP_GARAGE_ALERTS_FEATURE, attributes);
        return enabled;

    }

    public Boolean isSmsAlertsFeatureEnabled(String dealerId, String programId) {
        Map<String, String> attributes = new HashMap<>(
                Map.of("application", ATLAS, "dealerId", dealerId, "programId", programId));
        boolean enabled = isFeatureEnabled(ATLAS, ENABLE_SMS_GARAGE_ALERTS_FEATURE, attributes);
        return enabled;
    }

    public Boolean isGarageAlertsFeatureEnabled(String dealerId, String programId) {
        Map<String, String> attributes = new HashMap<>(
                Map.of("application", ATLAS, "dealerId", dealerId, "programId", programId));
        boolean enabled = isFeatureEnabled(ATLAS, ENABLE_GARAGE_ALERTS_FEATURE, attributes);
        return enabled;
    }

    public Boolean isEmailAlertsFeatureEnabled(String dealerId, String programId) {
        Map<String, String> attributes = new HashMap<>(
                Map.of("application", ATLAS, "dealerId", dealerId, "programId", programId));
        boolean enabled = isFeatureEnabled(ATLAS, ENABLE_EMAIL_GARAGE_ALERTS_FEATURE, attributes);
        return enabled;
    }

    public Boolean isAtlasDealerTrackPhase2Enabled(String dealerId) {
        Map<String, String> attributes = new HashMap<>(Map.of("application", ATLAS, "dealerId", dealerId));
        boolean enabled = isFeatureEnabled(ATLAS, ATLAS_DEALER_TRACK_PHASE2_ENABLED, attributes);
        return enabled;
    }

    public boolean isSendToCrmEnhancementFeatureEnabled() {
        return isFeatureEnabled(ATLAS, SEND_TO_CRM_ENHANCEMENT);
    }

    public boolean isEnableElasticNestedFields() {
        return isFeatureEnabled(ATLAS, ENABLE_ELASTIC_NESTED_FIELDS);
    }

    public boolean isCustomerDetailsPreQualificationsEnabled() {
        return isFeatureEnabled(ATLAS, CUSTOMER_DETAILS_PRE_QUALIFICATIONS);
    }
    public boolean isArNotesEnabled() {
        return isFeatureEnabled(ATLAS, ENABLE_AR_NOTES);
    }

    public boolean isActivityLogsEnabled() {
        return isFeatureEnabled(ATLAS, ENABLE_AR_ACTIVITY);
    }

    public boolean isArContractRequestEnabled() {
        return isFeatureEnabled(ATLAS, ENABLED_AR_CONTRACTS);
    }


    public boolean isARSearchesEnabled() {
        return isFeatureEnabled(ATLAS, ENABLE_AR_SEARCHES);
    }


    public Map<String, Boolean> getAllFeatureFlags(String loggedInEmail) {
        Map<String, Boolean> featureFlags = new HashMap<>();

        featureFlags.put("DEAL_EDITING_CASH_FEATURE", isFeatureEnabled(NISSAN, DEAL_EDITING_CASH_FEATURE));
        featureFlags.put("TRADE_IN_ADJUSTMENT_MULTIPLE_DEALERS_FEATURE",
                isFeatureEnabled(NISSAN, TRADE_IN_ADJUSTMENT_MULTIPLE_DEALERS_FEATURE));
        featureFlags.put("OFFER_ADJUSTMENT_FEATURE", isFeatureEnabled(NISSAN, OFFER_ADJUSTMENT_FEATURE));
        featureFlags.put("OFFER_ADJUSTMENT_FEATUREV2", isFeatureEnabled(UPGRADE, OFFER_ADJUSTMENT_FEATUREV2));
        featureFlags.put("SELL_AT_HOME_FEATURE", isFeatureEnabled(NISSAN, SELL_AT_HOME_FEATURE));
        featureFlags.put("VEHICLE_URL_DEALERIZE_VERSION", isFeatureEnabled(ATLAS, VEHICLE_URL_DEALERIZE_VERSION));
        featureFlags.put("CRM_LEADS_FOR_PROSPECTS", isFeatureEnabled(loggedInEmail, CRM_LEADS_FOR_PROSPECTS));
        featureFlags.put("NEW_CONFIG_MANAGER", isFeatureEnabled(loggedInEmail, NEW_CONFIG_MANAGER));
        featureFlags.put("ADAPTIVE_RETAIL_PLAYGROUND", isFeatureEnabled(loggedInEmail, ADAPTIVE_RETAIL_PLAYGROUND));
        featureFlags.put("ENABLE_STAGE_TILES_FOR_DEALERS", isFeatureEnabled(ATLAS, ENABLE_STAGE_TILES_FOR_DEALERS));
        featureFlags.put("ENABLE_STAGE_TILES_FOR_ADMINS", isFeatureEnabled(ATLAS, ENABLE_STAGE_TILES_FOR_ADMINS));
        featureFlags.put("LMS_PREFERENCE_FEATURE", isFeatureEnabled(NISSAN, LMS_PREFERENCE_FEATURE));
        featureFlags.put("ATLAS_CUSTOMER_SORT_FIELDS_ENABLED",
                isFeatureEnabled(loggedInEmail, ATLAS_CUSTOMER_SORT_FIELDS_ENABLED));
        featureFlags.put("ATLAS_ENHANCED_NO_GROUP_FILTERS",
                isFeatureEnabled(loggedInEmail, ATLAS_ENHANCED_NO_GROUP_FILTERS));
        featureFlags.put("ATLAS_SECOND_PHASE_FILTERS", isFeatureEnabled(loggedInEmail, ATLAS_SECOND_PHASE_FILTERS));
        featureFlags.put("ATLAS_RANGE_SLIDER_FILTERS", isFeatureEnabled(loggedInEmail, ATLAS_RANGE_SLIDER_FILTERS));
        featureFlags.put("ATLAS_CUSTOMER_BMW_NEW_COLUMNS",
                isFeatureEnabled(loggedInEmail, ATLAS_CUSTOMER_BMW_NEW_COLUMNS));
        featureFlags.put("ENABLE_ATLAS_TRADE_PURCHASE_FIELDS",
                isFeatureEnabled(ATLAS, ENABLE_ATLAS_TRADE_PURCHASE_FIELDS));
        featureFlags.put("ENABLE_NESNA_F_AND_I", isFeatureEnabled(loggedInEmail, ENABLE_NESNA_F_AND_I));
        featureFlags.put("DOMO_INTEGRATION", isDomoIntegrationEnabled());
        featureFlags.put("SEND_TO_CRM_ENHANCEMENT", isFeatureEnabled(ATLAS, SEND_TO_CRM_ENHANCEMENT));
        featureFlags.put("ATLAS_NEW_CUSTOMER_PAGE", isFeatureEnabled(loggedInEmail, ATLAS_NEW_CUSTOMER_PAGE));
        featureFlags.put("ATLAS_ACTION_BAR_ENABLED", isFeatureEnabled(ATLAS, ATLAS_ACTION_BAR_ENABLED));
        featureFlags.put("ATLAS_REQUEST_SCREEN_SHARE", isFeatureEnabled(ATLAS, ATLAS_REQUEST_SCREEN_SHARE));
        featureFlags.put("ENABLE_GARAGE_ALERTS_FEATURE", isFeatureEnabled(ATLAS, ENABLE_GARAGE_ALERTS_FEATURE));
        featureFlags.put("ENABLE_SMS_GARAGE_ALERTS_FEATURE", isFeatureEnabled(ATLAS, ENABLE_SMS_GARAGE_ALERTS_FEATURE));
        featureFlags.put("ENABLE_EMAIL_GARAGE_ALERTS_FEATURE",
                isFeatureEnabled(ATLAS, ENABLE_EMAIL_GARAGE_ALERTS_FEATURE));
        featureFlags.put("ENABLE_IN_APP_GARAGE_ALERTS_FEATURE",
                isFeatureEnabled(ATLAS, ENABLE_IN_APP_GARAGE_ALERTS_FEATURE));
        featureFlags.put("ATLAS_STICKY_FILTERS_ENABLED", isFeatureEnabled(ATLAS, ATLAS_STICKY_FILTERS_ENABLED));
        featureFlags.put("ATLAS_FAST_PASS_ENABLED", isFeatureEnabled(ATLAS, ATLAS_FAST_PASS_ENABLED));
        featureFlags.put("ATLAS_NEW_CUSTOMER_PAGE_ONLINE_NOW",
                isFeatureEnabled(loggedInEmail, ATLAS_NEW_CUSTOMER_PAGE_ONLINE_NOW));
        featureFlags.put("ATLAS_NEW_CUSTOMER_PAGE_IN_SHOWROOM",
                isFeatureEnabled(loggedInEmail, ATLAS_NEW_CUSTOMER_PAGE_IN_SHOWROOM));
        featureFlags.put("CUSTOMER_TAGS_ENHANCEMENT_TOGGLE",
                isFeatureEnabled(loggedInEmail, CUSTOMER_TAGS_ENHANCEMENT_TOGGLE));
        featureFlags.put("DRAWER_SETTINGS_ENABLED", isFeatureEnabled(loggedInEmail, DRAWER_SETTINGS_ENABLED));
        featureFlags.put("QR_CODE_SETTINGS_ENABLED", isFeatureEnabled(loggedInEmail, QR_CODE_SETTINGS_ENABLED));
        featureFlags.put("CUSTOMER_INFO_CARD_PROGRAM_USER_SUPPORT_TOGGLE",
                isFeatureEnabled(ATLAS, CUSTOMER_INFO_CARD_PROGRAM_USER_SUPPORT_TOGGLE));
        featureFlags.put("PROGRAM_USER_SUPPORT", isFeatureEnabled(ATLAS, PROGRAM_USER_SUPPORT));
        featureFlags.put("FINANCE_NEGATIVE_BUG", isFeatureEnabled(ATLAS, FINANCE_NEGATIVE_BUG));
        featureFlags.put("ATLAS_WALMART_INVENTORY_ENABLED",
                isFeatureEnabled(loggedInEmail, ATLAS_WALMART_INVENTORY_ENABLED));
        featureFlags.put("ATLAS_ENABLED_LEAD_FILTER", isFeatureEnabled(loggedInEmail, ATLAS_ENABLED_LEAD_FILTER));
        featureFlags.put("ATLAS_IN_APP_NOTIFICATION_ENABLED",
                isFeatureEnabled(loggedInEmail, ATLAS_IN_APP_NOTIFICATION_ENABLED));
        featureFlags.put("ENABLE_LANGUAGE_SELECTION", isFeatureEnabled(loggedInEmail, ENABLE_LANGUAGE_SELECTION));
        featureFlags.put("CUSTOMER_DETAILS_PRE_QUALIFICATIONS",
                isFeatureEnabled(ATLAS, CUSTOMER_DETAILS_PRE_QUALIFICATIONS));
        featureFlags.put("ENABLE_CARSAVER_F_AND_I", isFeatureEnabled(loggedInEmail, ENABLE_CARSAVER_F_AND_I));
        featureFlags.put("ENABLE_AR_NOTES", isFeatureEnabled(loggedInEmail, ENABLE_AR_NOTES));
        featureFlags.put("ATLAS_NEW_CUSTOMER_DETAILS_PAGE_CURRENT_VEHICLES", isFeatureEnabled(loggedInEmail, ATLAS_NEW_CUSTOMER_DETAILS_PAGE_CURRENT_VEHICLES));
        featureFlags.put("WHISP_LEAD_ENABLED", isFeatureEnabled(loggedInEmail, WHISP_LEAD_ENABLED));
        featureFlags.put("ENABLED_AR_CONTRACTS", isFeatureEnabled(ATLAS, ENABLED_AR_CONTRACTS));
        featureFlags.put("ATLAS_NEW_CUSTOMER_PAGE_IN_SHOWROOM_V2", isFeatureEnabled(ATLAS, ATLAS_NEW_CUSTOMER_PAGE_IN_SHOWROOM_V2));
        featureFlags.put("ENABLE_AR_FINANCE_APP", isFeatureEnabled(ATLAS, ENABLE_AR_FINANCE_APP));
        featureFlags.put("ENABLE_AR_ACTIVITY", isFeatureEnabled(ATLAS, ENABLE_AR_ACTIVITY));
        featureFlags.put("CTA_CONFIG_MENU", isFeatureEnabled(ATLAS, CTA_CONFIG_MENU));
        featureFlags.put("PROGRAM_RETURN_POLICY_FEATURE", isFeatureEnabled(ATLAS, PROGRAM_RETURN_POLICY_FEATURE));
        featureFlags.put("ENABLE_AR_SEARCHES", isFeatureEnabled(ATLAS, ENABLE_AR_SEARCHES));
        featureFlags.put("ENABLE_CLIENT_THEMING", isFeatureEnabled(ATLAS, ENABLE_CLIENT_THEMING));
        featureFlags.put("ENABLE_OVERLAY_SETTINGS", isFeatureEnabled(ATLAS, ENABLE_OVERLAY_SETTINGS));
        featureFlags.put("ENABLE_MDP_CONFIGS", isFeatureEnabled(ATLAS, ENABLE_MDP_CONFIGS));
        featureFlags.put("ENABLE_DEALER_SALES_PERSON", isFeatureEnabled(ATLAS, ENABLE_DEALER_SALES_PERSON));
        featureFlags.put("ENABLE_DEALER_PROGRAM_URLS", isFeatureEnabled(ATLAS, ENABLE_DEALER_PROGRAM_URLS));
        featureFlags.put("ENABLE_DEALER_RATE_SHEET", isFeatureEnabled(ATLAS, ENABLE_DEALER_RATE_SHEET));
        featureFlags.put("ATLAS_DEALER_TRACK_TOGGLE", isFeatureEnabled(ATLAS, ATLAS_DEALER_TRACK_TOGGLE));
        featureFlags.put("ENABLE_SMS_ONLINE_NOW", isFeatureEnabled(ATLAS, ENABLE_SMS_ONLINE_NOW));
        featureFlags.put("ATLAS_LEAD_TYPE_CONFIG", isFeatureEnabled(ATLAS, ATLAS_LEAD_TYPE_CONFIG));
        featureFlags.put("BOOST_PLUS_FEATURES_ENABLED", isFeatureEnabled(ATLAS, BOOST_PLUS_FEATURES_ENABLED));
        featureFlags.put("ENABLE_GRAPHICS_AND_IMAGES", isFeatureEnabled(ATLAS, ENABLE_GRAPHICS_AND_IMAGES));
        featureFlags.put("ATLAS_BANNER_CONFIG", isFeatureEnabled(ATLAS, ATLAS_BANNER_CONFIG));
        featureFlags.put("ATLAS_SERVICE_DRIVE_TOGGLE", isFeatureEnabled(ATLAS, ATLAS_SERVICE_DRIVE_TOGGLE));
        return featureFlags;
    }

    boolean isFeatureEnabled(String key, String featureName) {
        boolean isOn = false;

        try {
            isOn = splitClientFacade.isNewFeatureOn(key, featureName);
        } catch (Exception ex) {
            log.error("Atlas Split.io call error for feature: {}, Error: {}", featureName, ex);
        }

        return isOn;
    }

    boolean isFeatureEnabled(String key, String featureName, Map attributes) {
        boolean isOn = false;

        try {
            isOn = splitClientFacade.isNewFeatureOn(key, featureName, attributes);
        } catch (Exception ex) {
            log.error("Split.io call threw error for Nissan feature: {}, Error: {}", featureName, ex);
        }

        return isOn;
    }

    private String getProfile() {
        String profile = Optional.of(environment.getActiveProfiles()).flatMap(i -> Arrays.stream(i).findFirst())
                .orElse(null);
        return profile;
    }

    public boolean isRouteOneProtectionProductStagingHostEnabled() {
        boolean isEnabled = isFeatureEnabled(NISSAN, ROUTE_ONE_PROTECTION_PRODUCT_STAGING_HOST_ENABLED);
        return isEnabled;
    }

    private String getProductFromCampaign(CampaignView campaign) {
        if (campaign != null) {
            Optional<ProgramView> programView = programClient.findById(campaign.getProgramId());
            if (programView.isPresent() && programView.get().getProduct() != null) {
                ProductView productView = programView.get().getProduct();
                if (productView.getId().equals(UPGRADE_PRODUCT_ID)) {
                    return "Upgrade";
                } else if (productView.getId().equals(ECOMMERCE_PRODUCT_ID)) {
                    return "Nissan";
                }
            }

        }
        return "";
    }

    public boolean isImportCarLogsByDealerEnabled(String dealerId, Optional<UserView> loggedInUserId) {
        Map<String, String> attributes = new HashMap<>(Map.of("application", ATLAS, "dealerId", dealerId));
        loggedInUserId.ifPresent(it -> attributes.put("userId", it.getId()));

        return isFeatureEnabled(ATLAS, IMPORT_CAR_LOGS_BY_DEALER_V2, attributes);
    }

    public boolean isIncludeProspectLeadsWhispEnabled() {
        return isFeatureEnabled(ATLAS, WHISP_LEAD_ENABLED);
    }
    public Boolean isCarsaverFAndIEnabled() {
        boolean enabled = isFeatureEnabled(ATLAS, ENABLE_CARSAVER_F_AND_I);
        return enabled;
    }

    public boolean isAtlasNewCustomerDetailsPageCurrentVehiclesEnabled() {
        return isFeatureEnabled(ATLAS, ATLAS_NEW_CUSTOMER_DETAILS_PAGE_CURRENT_VEHICLES);
    }

    public boolean isDealerSalesPersonEnabled(String dealerId, Optional<UserView> loggedInUserId) {
        Map<String, String> attributes = new HashMap<>(Map.of("application", ATLAS, "dealerId", dealerId));
        loggedInUserId.ifPresent(it -> attributes.put("userId", it.getId()));
        return isFeatureEnabled(ATLAS, ENABLE_DEALER_SALES_PERSON, attributes);
    }

    public boolean isBoostPlusFeaturesEnabled() {
        return isFeatureEnabled(ATLAS, BOOST_PLUS_FEATURES_ENABLED);
    }
}
