package com.carsaver.partner.configuration.graphics.mappers

import com.carsaver.partner.configuration.graphics.GraphicsConfigRequest
import com.carsaver.partner.configuration.graphics.mappers.GraphicsConfigFileUtils.Companion.overrideFileNameAndSanitize
import org.springframework.web.multipart.MultipartFile
import java.util.*

class GraphicsConfigFileMapper {

    companion object {

        /**
         * Processes uploaded image files for Graphics configuration (logo, favicon, dealership images).
         *
         * This function:
         * 1. Validates that files are provided for graphics configuration
         * 2. Renames all files with a standardized format: "{dealerId}-{type}-{extension}"
         * 3. Associates each file with its corresponding property in the graphics configuration
         * 4. Sets the appropriate name properties with the new filename
         * 5. Override the original file with the overrodeFile
         *
         * @param dealerId The dealer identifier used to prefix filenames
         * @param graphicsConfig The graphics configuration object to be updated with file information
         * @param files List of files: [logo, favicon, dealership1, dealership2]
         * @return A flattened list of all non-null MultipartFile objects after processing
         */
        fun injectFiles(
            dealerId: String,
            graphicsConfig: GraphicsConfigRequest,
            files: MutableList<MultipartFile?>
        ): List<MultipartFile> {
            if (files.size != 4) {
                throw IllegalArgumentException("Bad request we expect 4 files for graphics config")
            }

            val fileTypes = listOf("logo", "favicon", "dealership-mobile", "dealership-desktop")
            val processedFiles = mutableListOf<MultipartFile>()

            for (i in files.indices) {
                val multipartFile = files[i]

                if (multipartFile != null) {
                    val fileType = fileTypes[i]

                    // Create overridden file with new name
                    val overrodeFile = overrideFileNameAndSanitize(multipartFile, i, dealerId, fileType.replace("-", ""))
                    files[i] = overrodeFile
                    processedFiles.add(overrodeFile)

                    // Set the appropriate properties in the graphics config
                    when (i) {
                        0 -> { // logo
                            graphicsConfig.logoName = overrodeFile.originalFilename?.let { Optional.of(it) }
                        }
                        1 -> { // favicon
                            graphicsConfig.favIconName = overrodeFile.originalFilename?.let { Optional.of(it) }
                        }
                        2 -> { // dealership mobile
                            if (graphicsConfig.dealershipImages == null) {
                                graphicsConfig.dealershipImages = Optional.of(GraphicsConfigRequest.DealershipImages())
                            }
                            graphicsConfig.dealershipImages?.get()?.mobileName = overrodeFile.originalFilename?.let { Optional.of(it) }
                        }
                        3 -> { // dealership desktop
                            if (graphicsConfig.dealershipImages == null) {
                                graphicsConfig.dealershipImages = Optional.of(GraphicsConfigRequest.DealershipImages())
                            }
                            graphicsConfig.dealershipImages?.get()?.desktopName = overrodeFile.originalFilename?.let { Optional.of(it) }
                        }
                    }
                }
            }

            return processedFiles
        }
    }
}
