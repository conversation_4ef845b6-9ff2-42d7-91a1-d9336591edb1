<template>
    <v-container v-if="loader.isLoading" class="fill-height">
        <v-row>
            <v-col cols="12">
                <div class="d-flex flex-column justify-center align-center">
                    <v-progress-circular :size="50" color="primary" indeterminate class="mb-2" />
                    <h1>Loading</h1>
                </div>
            </v-col>
        </v-row>
    </v-container>
    <v-container v-else-if="!loader.isLoading && !loader.isError" :key="dealerIds" fluid class="page-container">
        <message-listener @refresh-digit-retail-iframe="handleRefreshIframe" />
        <overlay />
        <v-row>
            <v-col cols="12" class="pb-0">
                <breadcrumbs :dealer-id="dealerIds" />
            </v-col>
        </v-row>

        <!--  ACTION BAR  -->
        <v-row v-if="isAtlasActionbarEnabled">
            <v-col cols="12" class="pb-0">
                <action-bar :dealer-id="dealerIds" :user-id="user.id" />
            </v-col>
        </v-row>
        <!--  ACTION BAR  -->

        <v-row>
            <v-col cols="12" class="pb-0">
                <CustomerInfoCard :dealer-id="dealerIds" :user-id="user.id"></CustomerInfoCard>
            </v-col>
        </v-row>
        <v-row>
            <v-col class="main-contain gap-12">
                <div class="section-container flex-shrink-0 customer-details-menu-wrapper order-lg-2">
                    <customer-details-menu :mobile="isMd" />
                </div>
                <div
                    v-if="getMappedComponent.isIframe"
                    class="iframe-container placeholder-container flex-grow-1 order-lg-1 bg-white"
                >
                    <!-- All content will go here -->
                    <digital-retail-iframe ref="drIframe" />
                </div>
                <div v-else class="placeholder-container flex-grow-1 order-lg-1">
                    <component :is="getMappedComponent.data" :dealer-id="dealerIds" :user-id="user.id" />
                </div>
            </v-col>
        </v-row>
    </v-container>
</template>

<script>
import { get, call } from "vuex-pathify";
import Breadcrumbs from "@/modules/Customers/components/CustomerDetails/Breadcrumbs";
import ActionBar from "Modules/Customers/components/CustomerDetails/ActionBar.vue";
import CustomerDetailsMenu from "@/modules/Customers/components/CustomerDetails/CustomerDetailsMenu";
import CustomerInfoCard from "@/modules/Customers/components/CustomerDetails/CustomerInfoCard.vue";
import lodashGet from "lodash/get";
import { COMPONENT_MAPPING, NAME_COMPONENT_MAPPING } from "@/modules/Customers/utils/customerDetails";
import DigitalRetailIframe from "Modules/Customers/components/CustomerDetails/DigitalRetailIframe";
import MessageListener from "Modules/Customers/components/CustomerDetails/MessageListener";
import Overlay from "Modules/Customers/components/CustomerDetails/Overlay";
import SendToCrmModal from "../components/CustomerDetails/SendToCrmModal";
import EventBus from "Util/eventBus";

export default {
    components: {
        SendToCrmModal,
        DigitalRetailIframe,
        CustomerDetailsMenu,
        Breadcrumbs,
        ActionBar,
        CustomerInfoCard,
        MessageListener,
        Overlay,
        [COMPONENT_MAPPING.LEADS]: () =>
            import("Modules/Customers/components/CustomerDetails/ContentSection/Leads/index.vue"),
        [COMPONENT_MAPPING.DOCUMENTS]: () =>
            import("Modules/Customers/components/CustomerDetails/ContentSection/Documents.vue"),
        [COMPONENT_MAPPING.PRE_APPROVALS]: () =>
            import("Modules/Customers/components/CustomerDetails/ContentSection/PreApprovals.vue"),
        [COMPONENT_MAPPING.VEHICLE_SALES]: () =>
            import("Modules/Customers/components/CustomerDetails/ContentSection/VehicleSales.vue"),
        [COMPONENT_MAPPING.DEFAULT]: () =>
            import("Modules/Customers/components/CustomerDetails/ContentSection/Default.vue"),
        [COMPONENT_MAPPING.CURRENT_VEHICLES]: () =>
            import("Modules/Customers/components/CustomerDetails/ContentSection/CurrentVehicles.vue"),
        [COMPONENT_MAPPING.FINANCE_APPLICATIONS]: () =>
            import("Modules/Customers/components/CustomerDetails/ContentSection/FinanceApplications/index.vue"),
        [COMPONENT_MAPPING.PRE_QUALIFICATIONS]: () =>
            import("Modules/Customers/components/CustomerDetails/ContentSection/PreQualifications.vue"),
        [COMPONENT_MAPPING.NOTES]: () =>
            import("Modules/Customers/components/CustomerDetails/ContentSection/Notes.vue"),
        [COMPONENT_MAPPING.CONTRACT_REQUESTS]: () =>
            import("Modules/Customers/components/CustomerDetails/ContentSection/ContractRequests.vue"),
        [COMPONENT_MAPPING.ACTIVITYLOGS]: () =>
            import("Modules/Customers/components/CustomerDetails/ContentSection/ActivityLogs/index.vue"),
        [COMPONENT_MAPPING.SEARCHES]: () =>
            import("Modules/Customers/components/CustomerDetails/ContentSection/Searches/index.vue"),
    },
    props: {
        userId: {
            type: String,
            required: true,
        },
        dealerId: {
            type: String,
            required: false,
            default: null,
        },
    },
    data() {
        return {
            selectedTradeId: null,
            selectedPreapproval: null,
            drawerShowing: false,
            currentDrawer: null,
            openSendToCrmModal: false,
        };
    },
    computed: {
        user: get("userDetails/userModel@data"),
        loader: get("userDetails/userModel@loader"),
        featureFlags: get("loggedInUser/featureFlags"),
        menuLinks: get("customerDetails/customerDetailsLinks@data"),
        activeLink: get("customerDetails/activeDetailsLink"), // integer 0 - (n-1)
        getMenuItem() {
            return this.menuLinks[this.activeLink] || {};
        },
        isSm() {
            return this.$vuetify.breakpoint.smAndDown;
        },
        isMd() {
            return this.$vuetify.breakpoint.mdAndDown;
        },
        isLg() {
            return this.$vuetify.breakpoint.lgAndDown;
        },
        dealerIds() {
            if (this.$route.params.dealerId) {
                return this.$route.params.dealerId;
            }

            if (this.$route.query.dealerIds) {
                return this.$route.query.dealerIds;
            }
            return null;
        },
        programIds() {
            if (this.$route.query.programIds) {
                return this.$route.query.programIds;
            }
            return null;
        },
        isAtlasActionbarEnabled() {
            const result = lodashGet(this.featureFlags, "ATLAS_ACTION_BAR_ENABLED", false) || false;
            return result;
        },
        getMappedComponent() {
            // TODO: Remove mapping if backend sends correct component name
            const component = NAME_COMPONENT_MAPPING[this.getMenuItem?.name];
            // check if component is available
            if (Object.values(COMPONENT_MAPPING).includes(component)) {
                return { isIframe: false, data: component };
            }
            if (this.getMenuItem?.url) {
                return { isIframe: true, data: this.getMenuItem.url };
            }
            return { isIframe: false, data: COMPONENT_MAPPING.DEFAULT };
        },
    },
    watch: {
        dealerIds(_value) {
            this.onLoad();
        },
        programIds(_value) {
            this.onLoad();
        },
    },
    methods: {
        openOverlay: call("customerDetails/openOverlay"),
        fetchCustomerDetailsLinks: call("customerDetails/fetchCustomerDetailsLinks"),
        handleRefreshIframe() {
            // Use the event bus to trigger the iframe refresh
            EventBus.$emit("refresh-digital-retail-iframe");

            // Also refresh the menu links data to update counts
            this.fetchCustomerDetailsLinks({ userId: this.user.id, dealerId: this.dealerIds });
        },
    },
};
</script>
<style lang="scss" scoped>
@import "../../../sass/customer-prospect-shared.scss";
</style>
