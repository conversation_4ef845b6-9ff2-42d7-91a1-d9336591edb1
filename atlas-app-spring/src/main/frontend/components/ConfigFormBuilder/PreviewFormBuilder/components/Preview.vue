<template>
    <v-card class="preview">
        <div class="preview-block d-flex flex-column justify-content-center h-100">
            <h2 class="preview-text">Preview</h2>
            <v-divider></v-divider>
            <div class="h-100">
                <slot> </slot>
            </div>
        </div>
    </v-card>
</template>

<script>
import { defineComponent } from "vue";

export default defineComponent({
    name: "Preview",
});
</script>

<style scoped lang="scss">
.preview {
    height: inherit;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    box-shadow: none !important;

    .preview-block {
        padding: 16px 24px 0 24px;
    }
}
.preview-text {
    font-size: px2rem(16);
    margin-bottom: 20px;
    line-height: 1.75;
}
</style>
