<template>
    <div class="logo-preview-wrapper py-6">
        <!-- Mobile Preview -->
        <div class="mobile-preview">
            <!-- Status Bar with Black Notch -->
            <div class="status-bar">
                <div class="time">10:01</div>
                <div class="black-notch"></div>
                <div class="status-icons">
                    <div class="signal-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="19" height="12" viewBox="0 0 19 12" fill="none">
                            <path
                                d="M10.75 3C10.75 2.44772 11.1977 2 11.75 2H12.75C13.3023 2 13.75 2.44772 13.75 3V11C13.75 11.5523 13.3023 12 12.75 12H11.75C11.1977 12 10.75 11.5523 10.75 11V3Z"
                                fill="black"
                            />
                            <path
                                d="M15.75 1C15.75 0.447715 16.1977 0 16.75 0H17.75C18.3023 0 18.75 0.447715 18.75 1V11C18.75 11.5523 18.3023 12 17.75 12H16.75C16.1977 12 15.75 11.5523 15.75 11V1Z"
                                fill="black"
                            />
                            <path
                                d="M5.75 6.5C5.75 5.94772 6.19772 5.5 6.75 5.5H7.75C8.30228 5.5 8.75 5.94772 8.75 6.5V11C8.75 11.5523 8.30228 12 7.75 12H6.75C6.19772 12 5.75 11.5523 5.75 11V6.5Z"
                                fill="black"
                            />
                            <path
                                d="M0.75 9C0.75 8.44772 1.19772 8 1.75 8H2.75C3.30228 8 3.75 8.44772 3.75 9V11C3.75 11.5523 3.30228 12 2.75 12H1.75C1.19772 12 0.75 11.5523 0.75 11V9Z"
                                fill="black"
                            />
                        </svg>
                    </div>
                    <div class="wifi-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="12" viewBox="0 0 18 12" fill="none">
                            <path
                                fill-rule="evenodd"
                                clip-rule="evenodd"
                                d="M9.25047 2.58753C11.717 2.58764 14.0893 3.55505 15.8769 5.28982C16.0115 5.42375 16.2266 5.42206 16.3592 5.28603L17.646 3.96045C17.7131 3.89146 17.7506 3.798 17.75 3.70076C17.7494 3.60353 17.7109 3.51052 17.643 3.44234C12.9511 -1.14745 5.54908 -1.14745 0.857163 3.44234C0.789197 3.51047 0.750634 3.60345 0.750008 3.70069C0.749381 3.79792 0.786742 3.89141 0.853824 3.96045L2.14096 5.28603C2.27346 5.42226 2.48878 5.42396 2.62331 5.28982C4.41116 3.55494 6.78367 2.58752 9.25047 2.58753ZM9.28591 6.67235C10.6411 6.67227 11.948 7.18644 12.9525 8.11497C13.0884 8.24674 13.3024 8.24389 13.4349 8.10853L14.7202 6.78295C14.7879 6.71342 14.8254 6.61909 14.8244 6.52108C14.8235 6.42306 14.784 6.32954 14.715 6.26142C11.6559 3.35683 6.91853 3.35683 3.85945 6.26142C3.79035 6.32953 3.75092 6.42311 3.75002 6.52115C3.74911 6.6192 3.7868 6.71352 3.85462 6.78295L5.13954 8.10853C5.27199 8.24389 5.48602 8.24674 5.62189 8.11497C6.62578 7.18706 7.93159 6.67293 9.28591 6.67235ZM11.8996 9.34267C11.9015 9.44096 11.8637 9.53573 11.7949 9.60459L9.57165 11.8948C9.50648 11.9621 9.41762 12 9.32491 12C9.2322 12 9.14334 11.9621 9.07817 11.8948L6.85452 9.60459C6.78583 9.53567 6.74804 9.44088 6.75008 9.34259C6.75212 9.2443 6.7938 9.15122 6.86528 9.08534C8.28515 7.8595 10.3647 7.8595 11.7845 9.08534C11.856 9.15128 11.8976 9.24438 11.8996 9.34267Z"
                                fill="black"
                            />
                        </svg>
                    </div>
                    <div class="battery-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="19" height="12" viewBox="0 0 19 12" fill="none">
                            <path
                                opacity="0.35"
                                d="M4.75 0.527344H13.75C15.6678 0.527344 17.2227 2.08222 17.2227 4V8C17.2227 9.91779 15.6678 11.4727 13.75 11.4727H4.75C2.83222 11.4727 1.27734 9.91778 1.27734 8V4C1.27734 2.08222 2.83221 0.527344 4.75 0.527344Z"
                                stroke="black"
                                stroke-width="1.05509"
                            />
                            <path
                                opacity="0.4"
                                d="M18.1953 4.61523V8.51094C18.667 8.18099 18.9737 7.41345 18.9737 6.56309C18.9737 5.71272 18.667 4.94518 18.1953 4.61523Z"
                                fill="black"
                            />
                            <path
                                d="M2.75 4C2.75 2.89543 3.64543 2 4.75 2H13.75C14.8546 2 15.75 2.89543 15.75 4V8C15.75 9.10457 14.8546 10 13.75 10H4.75C3.64543 10 2.75 9.10457 2.75 8V4Z"
                                fill="black"
                            />
                        </svg>
                    </div>
                </div>
            </div>

            <!-- URL Bar -->
            <div class="url-bar">
                <div class="lock-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="13" height="13" viewBox="0 0 13 13" fill="none">
                        <path
                            d="M4.32321 11.5H8.59866C9.178 11.5 9.46094 11.2048 9.46094 10.5589V7.17299C9.46094 6.59175 9.22741 6.2919 8.74687 6.24116V5.07868C8.74687 3.33957 7.63758 2.5 6.46094 2.5C5.28429 2.5 4.17501 3.33957 4.17501 5.07868V6.26422C3.73489 6.33342 3.46094 6.62865 3.46094 7.17299V10.5589C3.46094 11.2048 3.74387 11.5 4.32321 11.5ZM4.89806 4.9818C4.89806 3.82394 5.62112 3.2104 6.46094 3.2104C7.30076 3.2104 8.02381 3.82394 8.02381 4.9818V6.23655L4.89806 6.24116V4.9818Z"
                            fill="black"
                        />
                    </svg>
                </div>
                <div class="url">carsaver.com</div>
            </div>

            <!-- Navigation Bar -->
            <div class="navbar">
                <div class="hamburger-menu">
                    <div class="hamburger-dot"></div>
                    <div class="hamburger-dot"></div>
                    <div class="hamburger-dot"></div>
                </div>

                <div class="logo-container">
                    <img v-if="logoUrl" :src="logoUrl" alt="Logo Preview" class="preview-logo" />
                    <div v-else class="logo-circle">
                        <div class="logo-placeholder"></div>
                    </div>
                </div>

                <div class="nav-spacer"></div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    name: "LogoPreview",
    props: {
        logoUrl: {
            type: String,
            default: null,
        },
    },
};
</script>

<style scoped lang="scss">
.logo-preview-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.mobile-preview {
    width: 375px;
    background: white;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.status-bar {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 20px;
    background: #f5f5f5;
    font-size: 14px;
    font-weight: 600;
    color: #000;
    height: 24px;
    position: relative;
}

.black-notch {
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 120px;
    height: 20px;
    background: #000;
    border-radius: 0 0 12px 12px;
}

.url-bar {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 6px 20px;
    background: #f5f5f5;
    font-size: 13px;
    color: #333;
    gap: 4px;
}

.time {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", sans-serif;
}

.status-icons {
    display: flex;
    align-items: center;
    gap: 4px;
}

.signal-icon {
    display: flex;
    align-items: center;
}

.wifi-icon {
    display: flex;
    align-items: center;
    margin-left: 2px;
}

.battery-icon {
    display: flex;
    align-items: center;
    margin-left: 2px;
}

.lock-icon {
    display: flex;
    align-items: center;
    justify-content: center;
}

.url {
    font-weight: 500;
}

.navbar {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    background: white;
    border-bottom: 1px solid #e0e0e0;
    height: 60px;
}

.hamburger-menu {
    display: flex;
    flex-direction: column;
    gap: 3px;

    .hamburger-dot {
        width: 4px;
        height: 4px;
        background: #333;
        border-radius: 50%;
    }
}

.logo-container {
    display: flex;
    align-items: center;
    margin-left: 16px;
}

.logo-circle {
    width: 40px;
    height: 40px;
    background: #e3f2fd;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    position: relative;
}

.preview-logo {
    width: 40px;
    height: 40px;
    object-fit: contain;
}

.logo-placeholder {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 100%;
    height: 100%;
}

.nav-spacer {
    flex: 1; // Takes up remaining space
}

.preview-label {
    font-size: 12px;
    color: #666;
    font-weight: 500;
    margin-top: 8px;
}

// Responsive adjustments
@media (max-width: 768px) {
    .mobile-preview {
        width: 280px;
    }

    .status-bar {
        padding: 6px 16px;
        font-size: 13px;
    }

    .black-notch {
        width: 100px;
        height: 18px;
    }

    .url-bar {
        padding: 6px 16px;
        font-size: 12px;
    }

    .navbar {
        padding: 10px 16px;
        height: 50px;
    }

    .logo-circle {
        width: 36px;
        height: 36px;
    }

    .preview-logo {
        width: 36px;
        height: 36px;
    }
}
</style>
