<template>
    <CollapsiblePreviewFormBuilder :value="0" :title="`Smart Link / QR Code ${srNo}`" class="smart-links">
        <template #preview>
            <v-card flat class="preview-layout" :class="{ disable: !form.enabled }">
                <p class="title">Preview</p>
                <div class="qr-code-container">
                    <div class="qr-code">
                        <QrcodeVue
                            ref="qrcode"
                            :size="isMobile ? 200 : 300"
                            :value="qrCodeLink"
                            level="H"
                            render-as="svg"
                        />
                    </div>
                </div>
            </v-card>
        </template>
        <template #configuration>
            <v-form ref="form" v-model="valid" class="configuration-layout">
                <div class="col-span-2">
                    <ToggleBtn
                        :id="blockName + '/enabled'"
                        v-model="form.enabled"
                        label="Enable QR Code"
                        dense
                        class="enable-qr-toggle"
                    />
                </div>
                <div class="col-span-2">
                    <Dropdown
                        :id="blockName + '/program'"
                        v-model="form.program"
                        :items="allowedProgramValues"
                        item-text="label"
                        :disabled="!form.enabled"
                        label="Program"
                        outlined
                        dense
                        hide-details
                    />
                </div>
                <div class="col-span-2 input-field">
                    <InputField
                        :id="blockName + '/name'"
                        v-model="form.name"
                        label="Name"
                        outlined
                        :disabled="!form.enabled"
                        dense
                        :rules="[rules.required, rules.characterLimit]"
                        hint="Give your Smart Link/QR Code a name."
                        persistent-hint
                    />
                </div>

                <div class="col-span-2">
                    <Dropdown
                        :id="blockName + '/destination'"
                        v-model="form.destination"
                        :items="allowedDestinationValues"
                        :disabled="!form.enabled"
                        label="Destination"
                        outlined
                        dense
                        hide-details
                    />
                </div>

                <div class="col-span-2">
                    <Dropdown
                        :id="blockName + '/customerLocation'"
                        v-model="form.customerLocation"
                        :items="customerLocationOptions"
                        :disabled="!form.enabled"
                        label="Location"
                        outlined
                        dense
                        :rules="[rules.required]"
                        hide-details
                    />
                </div>

                <div
                    v-if="form.customerLocation === 'IN-STORE' || form.customerLocation === 'SERVICE DRIVE'"
                    class="col-span-2"
                >
                    <ToggleBtn
                        :id="blockName + '/refreshmentsEnabled'"
                        v-model="form.refreshmentsEnabled"
                        :disabled="!form.enabled"
                        label="Offer refreshments (coffee and water)"
                        dense
                    />
                </div>

                <div class="col-span-2">
                    <InputTextArea
                        :id="blockName + '/sms/initial'"
                        v-model="form.initialSms"
                        :disabled="!form.enabled"
                        outlined
                        label="Initial SMS Message"
                        :rules="[rules.required]"
                        hide-details="auto"
                        :rows="3"
                    />
                </div>
                <div class="col-span-2">
                    <InputTextArea
                        :id="blockName + '/sms/response'"
                        v-model="form.responseSms"
                        :disabled="!form.enabled"
                        outlined
                        label="Response SMS Message"
                        :rules="[rules.required]"
                        hide-details="auto"
                        :rows="3"
                    />
                </div>

                <div class="col-span-1 input-field">
                    <InputField
                        :id="blockName + '/sms/utmSource'"
                        v-model="form.utmSource"
                        :disabled="!form.enabled"
                        label="UTM Source"
                        placeholder="UTM Source"
                        outlined
                        dense
                        hide-details
                    />
                    <p>Optional</p>
                </div>
                <div class="col-span-1 input-field">
                    <InputField
                        :id="blockName + '/sms/utmMedium'"
                        v-model="form.utmMedium"
                        :disabled="!form.enabled"
                        label="UTM Medium"
                        placeholder="UTM Medium"
                        outlined
                        dense
                        hide-details
                    />
                    <p>Optional</p>
                </div>
                <div class="col-span-2 input-field">
                    <InputField
                        :id="blockName + '/sms/utmCampaign'"
                        v-model="form.utmCampaign"
                        :disabled="!form.enabled"
                        label="UTM Campaign"
                        placeholder="UTM Medium"
                        outlined
                        dense
                        hide-details
                    />
                    <p>Optional</p>
                </div>
            </v-form>
        </template>

        <template #footer>
            <v-btn color="primary" :disabled="!form.enabled" @click="handleDownload">DOWNLOAD QR CODE</v-btn>
            <v-btn outlined :disabled="!form.enabled" @click="handleCopySmartLink">COPY SMART LINK</v-btn>
        </template>
    </CollapsiblePreviewFormBuilder>
</template>

<script>
import { defineComponent } from "vue";
import { call, get } from "vuex-pathify";
import lodashSnakeCase from "lodash/snakeCase";
import CollapsiblePreviewFormBuilder from "Components/ConfigFormBuilder/CollapsiblePreviewFormBuilder/index.vue";
import QrcodeVue from "qrcode.vue";
import { NumberToWordsMapping } from "Util/ConfigManager/contants";
import Dropdown from "Components/FormInputs/Dropdown.vue";
import InputField from "Components/FormInputs/InputField.vue";
import ToggleBtn from "Components/FormInputs/ToggleBtn.vue";
import InputTextArea from "Components/FormInputs/InputTextArea.vue";
import { normalizeBoolean } from "Util/helpers";
import lodashGet from "lodash/get";

export default defineComponent({
    name: "QrCodeBlock",
    components: {
        ToggleBtn,
        InputTextArea,
        InputField,
        Dropdown,
        CollapsiblePreviewFormBuilder,
        QrcodeVue,
    },
    props: {
        srNo: {
            type: Number,
            required: true,
        },
        block: {
            type: Object,
            required: true,
        },
    },
    data() {
        return {
            programItems: [],
            destinationItems: [],
            valid: false,
            form: {
                enabled: false,
                program: null,
                destination: null,
                name: "",
                customerLocation: "ONLINE",
                refreshmentsEnabled: false,
                initialSms: "",
                responseSms: "",
                utmSource: "",
                utmMedium: "",
                utmCampaign: "",
            },
            rules: {
                characterLimit: (v) => v.length <= 100 || "Max 100 characters",
                required: (v) => !!v || "Required",
            },
        };
    },

    computed: {
        getFormData: get("pageConfigs/form"),
        selectedDealerId: get("loggedInUser/selectedDealer@id"),
        lastFormSubmitted: get("pageConfigs/validateForm@lastSubmitted"),
        blockName() {
            return `marketingSettings/smartLinkQrCode/${NumberToWordsMapping[this.srNo]}`;
        },
        environment() {
            return lodashGet(window, "_APP_CONFIG.env[0]", "local");
        },
        qrCodeLink() {
            const code = this.getDefaultsValue(this.blockName + "/link") || "#";
            const prodURL = `https://csvr.co/${code}`;
            const devURL = `https://beta.csvr.co/${code}`;
            return this.environment === "prod" ? prodURL : devURL;
        },
        allowedProgramValues() {
            return (
                this.block.components[0].inputs.find((input) => input.key === this.blockName + "/program")
                    ?.allowedValues || []
            );
        },
        allowedDestinationValues() {
            return (
                this.block.components[0].inputs.find((input) => input.key === this.blockName + "/destination")
                    ?.allowedValues || []
            );
        },
        customerLocationOptions() {
            return [
                { text: "ONLINE", value: "ONLINE" },
                { text: "IN-STORE", value: "IN-STORE" },
                { text: "SERVICE DRIVE", value: "SERVICE-DRIVE" },
            ];
        },

        isMobile() {
            return this.$vuetify.breakpoint.mdAndDown;
        },
    },
    watch: {
        valid(value) {
            this.handleValidateForm(value);
        },
        "form.enabled": {
            handler(value) {
                this.setValidateForm({ name: this.blockName, valid: this.valid });
                if (!value) {
                    this.setDefaults();
                    this.cleanObjectThatStartsWith();
                    this.removeValidateForm(this.blockName);
                } else {
                    this.$refs.form.resetValidation();
                }
            },
        },
        lastFormSubmitted(value) {
            if (value && this.form.enabled) {
                this.$refs.form.validate();
            }
        },
    },
    mounted() {
        this.form.enabled = normalizeBoolean(this.getDefaultsValue(this.blockName + "/enabled"), false);
        this.setDefaults();
    },
    methods: {
        setFormData: call("pageConfigs/setFormData"),
        setDataDefaultsMapping: call("pageConfigs/setDataDefaultsMapping"),
        setToastData: call("pageConfigs/setToastData"),
        setValidateForm: call("pageConfigs/setValidateForm"),
        removeValidateForm: call("pageConfigs/removeValidateForm"),
        handleCopySmartLink() {
            navigator.clipboard.writeText(this.qrCodeLink);
            this.setToastData({
                show: true,
                message: "This link has been copied to your clipboard.",
                type: "success",
            });
        },
        handleDownload() {
            // Access the SVG element
            const svgElement = this.$refs.qrcode.$el.querySelector("svg");

            // Serialize the SVG content to a string
            const svgData = new XMLSerializer().serializeToString(svgElement);

            // Create a download link for the SVG
            const downloadLink = document.createElement("a");
            downloadLink.href = `data:image/svg+xml;charset=utf-8,${encodeURIComponent(svgData)}`;
            downloadLink.download = `${this.selectedDealerId}_QRCode_${lodashSnakeCase(
                this.getDefaultsValue(this.blockName + "/name") || ""
            )}.svg`;

            // Programmatically click the download link
            downloadLink.click();

            // Show a success toast
            this.setToastData({
                show: true,
                message: "QR Code downloaded successfully as SVG.",
                type: "success",
            });
        },
        setDefaults() {
            // Load Default Values
            this.form.program = this.getDefaultsValue(this.blockName + "/program") || null;
            this.form.destination = this.getDefaultsValue(this.blockName + "/destination") || null;
            this.form.name = this.getDefaultsValue(this.blockName + "/name") || "";
            this.form.customerLocation = this.getDefaultsValue(this.blockName + "/customerLocation") || "ONLINE";
            this.form.refreshmentsEnabled = normalizeBoolean(
                this.getDefaultsValue(this.blockName + "/refreshmentsEnabled"),
                false
            );
            this.form.initialSms = this.getDefaultsValue(this.blockName + "/sms/initial") || "";
            this.form.responseSms = this.getDefaultsValue(this.blockName + "/sms/response") || "";
            this.form.utmSource = this.getDefaultsValue(this.blockName + "/sms/utmSource") || "";
            this.form.utmMedium = this.getDefaultsValue(this.blockName + "/sms/utmMedium") || "";
            this.form.utmCampaign = this.getDefaultsValue(this.blockName + "/sms/utmCampaign") || "";

            // if only one allowed Program value is present select that by default
            if (this.allowedProgramValues.length === 1) {
                this.form.program = this.allowedProgramValues[0].value;
            }
            // if only one allowed Destination value is present select that by default
            if (this.allowedDestinationValues.length === 1) {
                this.form.destination = this.allowedDestinationValues[0].value;
            }
        },
        getDefaultsValue(key) {
            return this.getFormData.defaults?.[key];
        },
        handleValidateForm(value) {
            if (this.form.enabled) this.setValidateForm({ name: this.blockName, valid: value });
        },
        cleanObjectThatStartsWith() {
            const obj = this.getFormData.data;
            const keys = Object.keys(obj);
            keys.forEach((key) => {
                if (key.startsWith(this.blockName)) {
                    delete obj[key];
                }
            });
            obj[this.blockName + "/enabled"] = false;
            this.setFormData(obj);
        },
    },
});
</script>

<style lang="scss">
@import "~vuetify/src/styles/settings/_variables";
// CUSTOM CSS
.smart-links {
    .v-text-field__details {
        margin-bottom: 0px !important;
    }
    .v-expansion-panel-content__wrap {
        @media #{map-get($display-breakpoints, 'sm-and-down')} {
            padding: 16px !important;
        }
    }
    .v-input--switch {
        margin: 0 !important;
        padding-left: 16px;
        width: fit-content;
    }
    > div {
        width: 100% !important;
    }
    p {
        margin: 0;
    }

    .input-field {
        display: flex;
        flex-direction: column;
        gap: 4px;
        p {
            color: #00000099;
            line-height: normal;
            font-size: 12px;
        }
    }

    .disable {
        pointer-events: none;
        opacity: 0.2;
    }

    // PREVIEW CARD
    .preview-layout {
        display: flex;
        flex-direction: column;
        border: 1px solid #e0e0e0;
        height: 100%;
        padding: 16px 24px 24px 24px;
        @media #{map-get($display-breakpoints, 'sm-and-down')} {
            padding: 12px;
        }
        border-radius: 4px !important;

        .title {
            font-size: px2rem(16) !important;
            padding-bottom: 16px;
            border-bottom: 1px solid #e0e0e0;
            font-weight: 600;
            line-height: 24px;
            letter-spacing: 0.024px;
            color: var(--grey-grey-darken-4, #212121);

            @media #{map-get($display-breakpoints, 'sm-and-down')} {
                font-size: px2rem(14) !important;
                padding-bottom: 8px;
                border: none;
                letter-spacing: 0.035px;
                font-weight: 500;
                line-height: 20px;
                color: var(--grey-grey-darken-3, #424242);
            }
        }

        .qr-code-container {
            display: grid;
            place-items: center;
            flex-grow: 1;
            margin-top: 24px;
            border-radius: 4px;
            border: 1px solid #e0e0e0;
            @media #{map-get($display-breakpoints, 'sm-and-down')} {
                margin-top: 0;
                border: none;
            }
            .qr-code {
                width: fit-content;
                img {
                    width: 300px;
                    height: 300px;
                    @media #{map-get($display-breakpoints, 'sm-and-down')} {
                        width: 200px;
                        height: 200px;
                    }
                }
                border-radius: 8px;
                padding: 4px;
                border: 1px solid #e0e0e0;
            }
        }
    }
    // PREVIEW CARD

    // CONFIGURATION LAYOUT
    .configuration-layout {
        display: grid;
        grid-template-columns: repeat(2, minmax(0, 1fr));
        gap: 16px;
        border: 1px solid #e0e0e0;
        height: fit-content;
        padding: 16px 24px;
        border-radius: 4px !important;
        @media #{map-get($display-breakpoints, 'sm-and-down')} {
            padding: 16px 8px;
        }
    }
    .col-span-2 {
        grid-column: span 2 / span 2;
    }
    .col-span-1 {
        grid-column: span 1 / span 1;
    }

    .configuration-layout-text {
        font-size: px2rem(16);
        margin-bottom: 4px;
    }
    .configuration-layout-description {
        font-size: px2rem(14);
        margin-bottom: 16px;
    }
    // CONFIGURATION LAYOUT

    // CONFIGURATION
    .configuration {
        margin-top: 24px;
    }
    .configuration-item {
        display: grid;
        grid-template-columns: repeat(2, minmax(0, 1fr));
        gap: 12px;
        @media #{map-get($display-breakpoints, 'sm-and-down')} {
            gap: 4px;
        }
    }
    .configuration-divider {
        grid-column: span 2 / span 2;
        margin: 0 16px 12px 16px;
    }
    .configuration-subItem {
        display: flex;
        align-items: center;
        gap: 8px;
    }
    .configuration-checkbox {
        margin: 0 !important;
        padding: 0 !important;
    }
    .configuration-footer {
        margin-top: 24px;
        margin-bottom: 0;
        font-size: px2rem(14);
        color: rgba(0, 0, 0, 0.6);
        font-style: italic;
    }
    .enable-qr-toggle {
        > div {
            padding-left: 0px !important;
        }
    }
    // CONFIGURATION
}
</style>
