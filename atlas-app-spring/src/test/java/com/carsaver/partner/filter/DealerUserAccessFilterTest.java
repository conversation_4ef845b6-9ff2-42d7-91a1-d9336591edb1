package com.carsaver.partner.filter;

import com.carsaver.core.DealerStatus;
import com.carsaver.magellan.auth.AuthUtils;
import com.carsaver.magellan.client.BasicUserAssociationClient;
import com.carsaver.magellan.client.DealerClient;
import com.carsaver.magellan.client.ProgramClient;
import com.carsaver.magellan.client.ProgramSubscriptionClient;
import com.carsaver.magellan.model.DealerView;
import com.carsaver.magellan.model.foundation.ProgramSubscriptionView;
import com.carsaver.magellan.model.foundation.ProgramView;
import com.carsaver.magellan.model.foundation.ProductView;
import com.carsaver.partner.client.FeatureSubscriptionsClient;
import com.carsaver.partner.model.FeatureSubscriptionResponse;
import com.carsaver.partner.reporting.service.ProgramService;
import com.carsaver.partner.security.SecurityUtils;
import com.carsaver.partner.service.PortalAutoLoginService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.springframework.hateoas.CollectionModel;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.mock.web.MockHttpServletResponse;
import org.springframework.test.util.ReflectionTestUtils;

import javax.servlet.FilterChain;
import javax.servlet.http.HttpSession;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import static com.carsaver.partner.filter.DealerUserAccessFilter.*;

class DealerUserAccessFilterTest {

    private DealerUserAccessFilter dealerUserAccessFilter;
    private ProgramSubscriptionClient programSubscriptionClient;
    private FeatureSubscriptionsClient featureSubscriptionsClient;
    private ProgramClient programClient;
    private DealerClient dealerClient;
    private BasicUserAssociationClient basicUserAssociationClient;
    private ProgramService programService;

    private static final String DEALER_ID = "123";
    private static final String NISSAN_PROGRAM_ID = "nissan-bah";
    private static final String BOOST_FEATURE_ID = "nissan-bah";
    private static final String BOOST_PROGRAM_ID = "boost-prog";
    private static final String OTHER_PROGRAM_ID = "other-prog";

    @BeforeEach
    void setUp() {
        dealerUserAccessFilter = new DealerUserAccessFilter();
        programSubscriptionClient = Mockito.mock(ProgramSubscriptionClient.class);
        programClient = Mockito.mock(ProgramClient.class);
        featureSubscriptionsClient = Mockito.mock(FeatureSubscriptionsClient.class);
        dealerClient = Mockito.mock(DealerClient.class);
        basicUserAssociationClient = Mockito.mock(BasicUserAssociationClient.class);
        programService = Mockito.mock(ProgramService.class);

        // Use ReflectionTestUtils to inject mocks and values
        ReflectionTestUtils.setField(dealerUserAccessFilter, "programSubscriptionClient", programSubscriptionClient);
        ReflectionTestUtils.setField(dealerUserAccessFilter, "programClient", programClient);
        ReflectionTestUtils.setField(dealerUserAccessFilter, "dealerClient", dealerClient);
        ReflectionTestUtils.setField(dealerUserAccessFilter, "basicUserAssociationClient", basicUserAssociationClient);
        ReflectionTestUtils.setField(dealerUserAccessFilter, "programService", programService);
        ReflectionTestUtils.setField(dealerUserAccessFilter, "nissanBuyAtHomeProgramId", NISSAN_PROGRAM_ID);
        ReflectionTestUtils.setField(dealerUserAccessFilter, "boostFeaturesFeatureId", BOOST_FEATURE_ID);
        ReflectionTestUtils.setField(dealerUserAccessFilter, "featureSubscriptionsClient", featureSubscriptionsClient);
    }

    @Test
    void whenDealerIsEnrolledInBoost_thenReturnTrue() {
        // Arrange
        ProgramSubscriptionView subscription = new ProgramSubscriptionView();
        subscription.setStatus(DealerStatus.LIVE);
        subscription.setProgramId(BOOST_PROGRAM_ID);
        CollectionModel<ProgramSubscriptionView> subscriptions = CollectionModel.of(List.of(subscription));

        ProgramView program = new ProgramView();
        ProductView product = new ProductView();
        product.setId(DealerUserAccessFilter.ECOMMERCE_PRODUCT_ID);
        program.setProduct(product);

        when(programSubscriptionClient.findByDealer(DEALER_ID)).thenReturn(subscriptions);
        when(programClient.findById(BOOST_PROGRAM_ID)).thenReturn(Optional.of(program));

        // Act
        boolean result = dealerUserAccessFilter.isDealerEnrolledInBoost(DEALER_ID);

        // Assert
        assertTrue(result);
    }

    @Test
    void whenSubscriptionIsInactive_thenReturnFalse() {
        // Arrange
        ProgramSubscriptionView subscription = new ProgramSubscriptionView();
        subscription.setStatus(DealerStatus.CANCELLED);
        subscription.setProgramId(BOOST_PROGRAM_ID);
        CollectionModel<ProgramSubscriptionView> subscriptions = CollectionModel.of(List.of(subscription));

        when(programSubscriptionClient.findByDealer(DEALER_ID)).thenReturn(subscriptions);

        // Act
        boolean result = dealerUserAccessFilter.isDealerEnrolledInBoost(DEALER_ID);

        // Assert
        assertFalse(result);
    }

    @Test
    void whenSubscriptionIsNissanBuyAtHome_thenReturnFalse() {
        // Arrange
        ProgramSubscriptionView subscription = new ProgramSubscriptionView();
        subscription.setStatus(DealerStatus.LIVE);
        subscription.setProgramId(NISSAN_PROGRAM_ID);
        CollectionModel<ProgramSubscriptionView> subscriptions = CollectionModel.of(List.of(subscription));

        when(programSubscriptionClient.findByDealer(DEALER_ID)).thenReturn(subscriptions);

        // Act
        boolean result = dealerUserAccessFilter.isDealerEnrolledInBoost(DEALER_ID);

        // Assert
        assertFalse(result);
    }

    @Test
    void whenSubscriptionIsNissanBuyAtHome_andHasFeatureSubscriptionActive_thenReturnTrue() {
        // Arrange
        ProgramSubscriptionView subscription = new ProgramSubscriptionView();
        subscription.setStatus(DealerStatus.LIVE);
        subscription.setProgramId(NISSAN_PROGRAM_ID);
        CollectionModel<ProgramSubscriptionView> subscriptions = CollectionModel.of(List.of(subscription));

        when(programSubscriptionClient.findByDealer(DEALER_ID)).thenReturn(subscriptions);
        when(featureSubscriptionsClient.getFeatureSubscription(any())).thenReturn(FeatureSubscriptionResponse.builder().active(true).build());
        // Act
        boolean result = dealerUserAccessFilter.isDealerEnrolledInBoost(DEALER_ID);

        // Assert
        assertTrue(result);
    }

    @Test
    void whenSubscriptionIsNissanBuyAtHome_andHasFeatureSubscriptionInactive_thenReturnFalse() {
        // Arrange
        ProgramSubscriptionView subscription = new ProgramSubscriptionView();
        subscription.setStatus(DealerStatus.LIVE);
        subscription.setProgramId(NISSAN_PROGRAM_ID);
        CollectionModel<ProgramSubscriptionView> subscriptions = CollectionModel.of(List.of(subscription));

        when(programSubscriptionClient.findByDealer(DEALER_ID)).thenReturn(subscriptions);
        when(featureSubscriptionsClient.getFeatureSubscription(any())).thenReturn(FeatureSubscriptionResponse.builder().active(false).build());
        // Act
        boolean result = dealerUserAccessFilter.isDealerEnrolledInBoost(DEALER_ID);

        // Assert
        assertFalse(result);
    }

    @Test
    void whenProductIsNotEcommerce_thenReturnFalse() {
        // Arrange
        ProgramSubscriptionView subscription = new ProgramSubscriptionView();
        subscription.setStatus(DealerStatus.LIVE);
        subscription.setProgramId(OTHER_PROGRAM_ID);
        CollectionModel<ProgramSubscriptionView> subscriptions = CollectionModel.of(List.of(subscription));

        ProgramView program = new ProgramView();
        ProductView product = new ProductView();
        product.setId(999); // Not an e-commerce product ID
        program.setProduct(product);

        when(programSubscriptionClient.findByDealer(DEALER_ID)).thenReturn(subscriptions);
        when(programClient.findById(OTHER_PROGRAM_ID)).thenReturn(Optional.of(program));

        // Act
        boolean result = dealerUserAccessFilter.isDealerEnrolledInBoost(DEALER_ID);

        // Assert
        assertFalse(result);
    }

    @Test
    void whenDealerHasNoSubscriptions_thenReturnFalse() {
        // Arrange
        when(programSubscriptionClient.findByDealer(DEALER_ID)).thenReturn(CollectionModel.of(Collections.emptyList()));

        // Act
        boolean result = dealerUserAccessFilter.isDealerEnrolledInBoost(DEALER_ID);

        // Assert
        assertFalse(result);
    }

    @Test
    void whenClientThrowsException_thenReturnFalse() {
        // Arrange
        when(programSubscriptionClient.findByDealer(DEALER_ID)).thenThrow(new RuntimeException("API error"));

        // Act
        boolean result = dealerUserAccessFilter.isDealerEnrolledInBoost(DEALER_ID);

        // Assert
        assertFalse(result);
    }

    @Test
    void whenProgramIsNotFound_thenReturnFalse() {
        // Arrange
        ProgramSubscriptionView subscription = new ProgramSubscriptionView();
        subscription.setStatus(DealerStatus.LIVE);
        subscription.setProgramId(BOOST_PROGRAM_ID);
        CollectionModel<ProgramSubscriptionView> subscriptions = CollectionModel.of(List.of(subscription));

        when(programSubscriptionClient.findByDealer(DEALER_ID)).thenReturn(subscriptions);
        when(programClient.findById(BOOST_PROGRAM_ID)).thenReturn(Optional.empty());

        // Act
        boolean result = dealerUserAccessFilter.isDealerEnrolledInBoost(DEALER_ID);

        // Assert
        assertFalse(result);
    }

    // New tests for Admin user session handling
    @Test
    void whenAdminUserWithDealerIdParam_thenSetSessionAttributes() throws Exception {
        // Arrange
        MockHttpServletRequest request = new MockHttpServletRequest();
        MockHttpServletResponse response = new MockHttpServletResponse();
        FilterChain filterChain = mock(FilterChain.class);
        HttpSession session = mock(HttpSession.class);

        request.setParameter("dealerIds", DEALER_ID);
        request.setSession(session);

        DealerView dealer = new DealerView();
        dealer.setId(DEALER_ID);
        dealer.setName("Test Dealer");

        try (MockedStatic<AuthUtils> authUtilsMock = mockStatic(AuthUtils.class);
             MockedStatic<SecurityUtils> securityUtilsMock = mockStatic(SecurityUtils.class)) {

            authUtilsMock.when(AuthUtils::getUserIdFromSecurityContext).thenReturn(Optional.of("user123"));
            securityUtilsMock.when(SecurityUtils::isAdminUser).thenReturn(true);
            securityUtilsMock.when(SecurityUtils::isDealerUser).thenReturn(false);
            securityUtilsMock.when(SecurityUtils::isProgramUser).thenReturn(false);

            when(session.getAttribute(DEALER_LIST)).thenReturn(null);
            when(dealerClient.findById(DEALER_ID)).thenReturn(dealer);

            // Act
            dealerUserAccessFilter.doFilterInternal(request, response, filterChain);

            // Assert
            verify(session).setAttribute(eq(DEALER_LIST), eq(Arrays.asList(dealer)));
            verify(session).setAttribute(eq(DEALER_LIST_WITH_BOOST_CHECK), any());
            verify(filterChain).doFilter(request, response);
        }
    }

    @Test
    void whenAdminUserWithExistingSessionData_thenPreserveSessionData() throws Exception {
        // Arrange
        MockHttpServletRequest request = new MockHttpServletRequest();
        MockHttpServletResponse response = new MockHttpServletResponse();
        FilterChain filterChain = mock(FilterChain.class);
        HttpSession session = mock(HttpSession.class);

        request.setSession(session);

        DealerView existingDealer = new DealerView();
        existingDealer.setId("existing-dealer");
        List<DealerView> existingDealerList = Arrays.asList(existingDealer);

        try (MockedStatic<AuthUtils> authUtilsMock = mockStatic(AuthUtils.class);
             MockedStatic<SecurityUtils> securityUtilsMock = mockStatic(SecurityUtils.class)) {

            authUtilsMock.when(AuthUtils::getUserIdFromSecurityContext).thenReturn(Optional.of("user123"));
            securityUtilsMock.when(SecurityUtils::isAdminUser).thenReturn(true);
            securityUtilsMock.when(SecurityUtils::isDealerUser).thenReturn(false);
            securityUtilsMock.when(SecurityUtils::isProgramUser).thenReturn(false);

            when(session.getAttribute(DEALER_LIST)).thenReturn(existingDealerList);
            when(session.getAttribute(PROGRAMS_LIST)).thenReturn(Collections.emptyList());

            // Act
            dealerUserAccessFilter.doFilterInternal(request, response, filterChain);

            // Assert - should NOT set empty lists since existing data exists
            verify(session, never()).setAttribute(eq(DEALER_LIST), eq(Collections.emptyList()));
            verify(session, never()).setAttribute(eq(DEALER_LIST_WITH_BOOST_CHECK), eq(Collections.emptyList()));
            verify(session, never()).setAttribute(eq(PROGRAMS_LIST), eq(Collections.emptyList()));
            verify(filterChain).doFilter(request, response);
        }
    }

    @Test
    void whenAdminUserWithNoExistingDataAndNoDealerId_thenSetEmptyLists() throws Exception {
        // Arrange
        MockHttpServletRequest request = new MockHttpServletRequest();
        MockHttpServletResponse response = new MockHttpServletResponse();
        FilterChain filterChain = mock(FilterChain.class);
        HttpSession session = mock(HttpSession.class);

        request.setSession(session);

        try (MockedStatic<AuthUtils> authUtilsMock = mockStatic(AuthUtils.class);
             MockedStatic<SecurityUtils> securityUtilsMock = mockStatic(SecurityUtils.class)) {

            authUtilsMock.when(AuthUtils::getUserIdFromSecurityContext).thenReturn(Optional.of("user123"));
            securityUtilsMock.when(SecurityUtils::isAdminUser).thenReturn(true);
            securityUtilsMock.when(SecurityUtils::isDealerUser).thenReturn(false);
            securityUtilsMock.when(SecurityUtils::isProgramUser).thenReturn(false);

            when(session.getAttribute(DEALER_LIST)).thenReturn(null);
            when(session.getAttribute(PROGRAMS_LIST)).thenReturn(null);

            // Act
            dealerUserAccessFilter.doFilterInternal(request, response, filterChain);

            // Assert - should set empty lists only when no existing data
            verify(session).setAttribute(eq(DEALER_LIST), eq(Collections.emptyList()));
            verify(session).setAttribute(eq(DEALER_LIST_WITH_BOOST_CHECK), eq(Collections.emptyList()));
            verify(session).setAttribute(eq(PROGRAMS_LIST), eq(Collections.emptyList()));
            verify(filterChain).doFilter(request, response);
        }
    }

    @Test
    void whenRegularDealerUser_thenBehaviorUnchanged() throws Exception {
        // Arrange
        MockHttpServletRequest request = new MockHttpServletRequest();
        MockHttpServletResponse response = new MockHttpServletResponse();
        FilterChain filterChain = mock(FilterChain.class);
        HttpSession session = mock(HttpSession.class);

        request.setSession(session);

        try (MockedStatic<AuthUtils> authUtilsMock = mockStatic(AuthUtils.class);
             MockedStatic<SecurityUtils> securityUtilsMock = mockStatic(SecurityUtils.class)) {

            authUtilsMock.when(AuthUtils::getUserIdFromSecurityContext).thenReturn(Optional.of("user123"));
            securityUtilsMock.when(SecurityUtils::isAdminUser).thenReturn(false);
            securityUtilsMock.when(SecurityUtils::isDealerUser).thenReturn(true);
            securityUtilsMock.when(SecurityUtils::isProgramUser).thenReturn(false);

            when(session.getAttribute(DEALER_LIST)).thenReturn(null);
            when(basicUserAssociationClient.findByUserId(anyString())).thenReturn(CollectionModel.of(Collections.emptyList()));
            when(programService.getPrograms(anyString())).thenReturn(Collections.emptyList());

            // Act
            dealerUserAccessFilter.doFilterInternal(request, response, filterChain);

            // Assert - regular dealer user behavior should be unchanged
            verify(basicUserAssociationClient).findByUserId("user123");
            verify(programService).getPrograms("user123");
            verify(filterChain).doFilter(request, response);
        }
    }
}
